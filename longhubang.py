import asyncio
import json
from urllib.parse import urlencode

import aiohttp
import aiomysql
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MySQL配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'cjlike88',
    'db': 'stock',
    'charset': 'utf8mb4',
    'autocommit': True
}

# API配置
API_URL = 'https://applhb.longhuvip.com/w1/api/index.php'
HEADERS = {
    "host": "applhb.longhuvip.com",
    "content-type": "application/x-www-form-urlencoded; charset=utf-8",
    "connection": "keep-alive",
    "accept": "*/*",
    "user-agent": "lhb/5.20.7 (com.kaipanla.www; build:0; iOS 17.6.0) Alamofire/4.9.1",
    "accept-language": "zh-Hans-CN;q=1.0",
}


async def fetch_longhubang_data(session, date):
    """获取指定日期的龙虎榜数据"""
    params = {
        'Index': '0',
        'PhoneOSNew': '2',
        'Time': date,
        'VerSion': '********',
        'a': 'GetStockList',
        'apiv': 'w41',
        'c': 'LongHuBang',
        'st': '300'
    }
    # 将字典转换为URL编码的字符串
    data_str = urlencode(params)
    try:
        async with session.post(API_URL, headers=HEADERS, data=data_str) as response:
            if response.status != 200:
                logger.error(f"请求失败，状态码: {response.status}, 日期: {date}")
                return None
            result = await response.text()
            result = json.loads(result)
            return result.get('list', [])
    except Exception as e:
        logger.error(f"获取龙虎榜数据异常，日期: {date}, 错误: {str(e)}")
        return None


async def check_date_exists(pool, date):
    """检查指定日期的数据是否已存在"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT COUNT(*) FROM longhu_bang WHERE trade_date = %s",
                (date,)
            )
            result = await cursor.fetchone()
            return result[0] > 0


async def save_to_mysql(pool, data_list, trade_date):
    """将数据保存到MySQL"""
    if not data_list:
        return

    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            # 准备批量插入数据
            values = []
            for item in data_list:
                values.append((
                    item['ID'],
                    item['Name'],
                    item['IncreaseAmount'],
                    int(item['D3']),
                    float(item['BuyIn']),
                    int(item['JoinNum']),
                    float(item['Turnover']),
                    float(item['CircPrice']),
                    item['Amplitude'],
                    item['TurnoverRatio'],
                    float(item['Capitalization']),
                    trade_date
                ))

            # 执行批量插入，忽略重复数据
            sql = """
            INSERT IGNORE INTO longhu_bang 
            (stock_id, stock_name, increase_amount, d3, buy_in, join_num, turnover, 
             circ_price, amplitude, turnover_ratio, capitalization, trade_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            await cursor.executemany(sql, values)
            logger.info(f"成功插入 {len(data_list)} 条数据，日期: {trade_date}")


async def process_date(session, pool, date):
    """处理单个日期的数据"""
    # 首先检查该日期的数据是否已存在
    exists = await check_date_exists(pool, date)
    if exists:
        logger.info(f"数据已存在，跳过处理日期: {date}")
        return
    logger.info(f"开始处理日期: {date}")
    data_list = await fetch_longhubang_data(session, date)
    if data_list:
        await save_to_mysql(pool, data_list, date)
    else:
        logger.warning(f"未获取到数据，日期: {date}")


async def main():
    # 计算日期范围：从今天往前推10年
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=365 * 25)

    # 生成所有日期列表
    date_list = []
    current_date = start_date
    while current_date <= end_date:
        date_list.append(current_date.strftime('%Y-%m-%d'))
        current_date += timedelta(days=1)

    # 创建连接池和session
    async with aiomysql.create_pool(**MYSQL_CONFIG) as pool:
        async with aiohttp.ClientSession() as session:
            # 并发处理所有日期
            tasks = [process_date(session, pool, date) for date in date_list]
            await asyncio.gather(*tasks)


if __name__ == '__main__':
    asyncio.run(main())
