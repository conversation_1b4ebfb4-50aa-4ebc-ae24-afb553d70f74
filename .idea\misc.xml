<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Black">
    <option name="sdkName" value="Python 3.9" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Async code and promisesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>CSS</id>
          </State>
          <State>
            <id>Code quality toolsCSS</id>
          </State>
          <State>
            <id>Code quality toolsJavaScript and TypeScript</id>
          </State>
          <State>
            <id>DOM issuesJavaScript and TypeScript</id>
          </State>
          <State>
            <id>Google App Engine (Python)</id>
          </State>
          <State>
            <id>JavaScript and TypeScript</id>
          </State>
          <State>
            <id>Jupyter</id>
          </State>
          <State>
            <id>Python</id>
          </State>
          <State>
            <id>SQL</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>Angular</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" project-jdk-name="Python 3.9" project-jdk-type="Python SDK" />
</project>