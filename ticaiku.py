import asyncio
from urllib.parse import urlencode
from datetime import datetime

import aiomysql
import aiohttp
import json


async def insert_data_into_db(data):
    # MySQL configuration
    MYSQL_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'cjlike88',
        'db': 'stock',
        'charset': 'utf8mb4',
        'autocommit': True
    }

    try:
        # Create a connection pool
        pool = await aiomysql.create_pool(**MYSQL_CONFIG)

        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Prepare SQL statement (updated to include update_time)
                sql = """
                INSERT INTO ticaiku_table (
                    ticaiku_id, ticaiku_name, ticaiku_desc,
                    level1_id, level1_name,
                    level2_id, level2_name,
                    stock_id, stock_name, stock_select_reason,
                    update_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                # Execute insert for each record
                await cursor.executemany(sql, data)

        print(f"Successfully inserted {len(data)} records!")
    except Exception as e:
        print(f"Error inserting data: {e}")
    finally:
        if 'pool' in locals():
            pool.close()
            await pool.wait_closed()


async def fetch_ticaiku_data(session, ticaiku_id):
    url = 'https://applhb.longhuvip.com/w1/api/index.php'
    headers = {
        "host": "applhb.longhuvip.com",
        "content-type": "application/x-www-form-urlencoded; charset=utf-8",
        "connection": "keep-alive",
        "accept": "*/*",
        "user-agent": "lhb/5.20.7 (com.kaipanla.www; build:0; iOS 17.6.0) Alamofire/4.9.1",
        "accept-language": "zh-Hans-CN;q=1.0",
        "accept-encoding": "gzip;q=1.0, compress;q=0.5"
    }

    data = {
        'DeviceID': '3fa2ad4aa0dcd40879fa95abce37ac74a43bbc1e',
        'ID': str(ticaiku_id),
        'PhoneOSNew': '2',
        'Token': 'fcab416449e71f72ca8d3ee783ddbf09',
        'UserID': '3892697',
        'VerSion': '********',
        'a': 'InfoGet',
        'apiv': 'w41',
        'c': 'Theme'
    }
    # 将字典转换为URL编码的字符串
    data_str = urlencode(data)
    try:
        async with session.post(url, headers=headers, data=data_str) as response:
            if response.status == 200:
                result = await response.text()
                result = json.loads(result)
                return result
            else:
                print(f"Failed to fetch data for ID {ticaiku_id}, status: {response.status}")
                return None
    except Exception as e:
        print(f"Error fetching data for ID {ticaiku_id}: {e}")
        return None


def convert_timestamp(timestamp):
    """Convert Unix timestamp to datetime string"""
    try:
        return datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')
    except:
        return None


async def process_ticaiku_data(data):
    if not data or 'Table' not in data or not data['Table']:
        return []

    ticaiku_id = data.get('ID', '')
    ticaiku_name = data.get('Name', '')
    ticaiku_desc = data.get('BriefIntro', '')
    update_time = convert_timestamp(data.get('UpdateTime'))  # Convert timestamp to datetime

    data_to_insert = []
    ticaiku_table = data['Table']

    for item in ticaiku_table:
        # Process Level1 data
        level1 = item.get('Level1', {})
        if level1:
            level1_id = level1.get('ID', '')
            level1_name = level1.get('Name', '')
            level1_stocks = level1.get('Stocks', [])

            for stock in level1_stocks:
                stock_id = stock.get('StockID', '')
                stock_name = stock.get('prod_name', '')
                select_reason = stock.get('Reason', '')

                data_to_insert.append((
                    ticaiku_id, ticaiku_name, ticaiku_desc,
                    level1_id, level1_name,
                    None, None,  # No level2 data
                    stock_id, stock_name, select_reason,
                    update_time  # Add converted datetime
                ))

        # Process Level2 data
        level2 = item.get('Level2', [])
        if level2:
            for l2_item in level2:
                level2_id = l2_item.get('ID', '')
                level2_name = l2_item.get('Name', '')
                level2_stocks = l2_item.get('Stocks', [])

                for stock in level2_stocks:
                    stock_id = stock.get('StockID', '')
                    stock_name = stock.get('prod_name', '')
                    select_reason = stock.get('Reason', '')

                    data_to_insert.append((
                        ticaiku_id, ticaiku_name, ticaiku_desc,
                        level1_id, level1_name,
                        level2_id, level2_name,
                        stock_id, stock_name, select_reason,
                        update_time  # Add converted datetime
                    ))

    return data_to_insert


async def process_single_id(session, ticaiku_id):
    print(f"Processing ID: {ticaiku_id}")
    result = await fetch_ticaiku_data(session, ticaiku_id)
    if result:
        processed_data = await process_ticaiku_data(result)
        if processed_data:
            await insert_data_into_db(processed_data)
        else:
            print(f"No valid data found for ID {ticaiku_id}")
    else:
        print(f"Failed to fetch data for ID {ticaiku_id}")

    # Wait 1 second before processing next ID
    await asyncio.sleep(1)


async def main():
    start_id = 1
    end_id = 400

    async with aiohttp.ClientSession() as session:
        for ticaiku_id in range(start_id, end_id + 1):
            await process_single_id(session, ticaiku_id)


if __name__ == '__main__':
    asyncio.run(main())
