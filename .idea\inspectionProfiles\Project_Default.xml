<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="167" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="ws4py" />
            <item index="1" class="java.lang.String" itemvalue="async-timeout" />
            <item index="2" class="java.lang.String" itemvalue="logger" />
            <item index="3" class="java.lang.String" itemvalue="requests" />
            <item index="4" class="java.lang.String" itemvalue="loguru" />
            <item index="5" class="java.lang.String" itemvalue="redis" />
            <item index="6" class="java.lang.String" itemvalue="win32-setctime" />
            <item index="7" class="java.lang.String" itemvalue="certifi" />
            <item index="8" class="java.lang.String" itemvalue="aiohttp" />
            <item index="9" class="java.lang.String" itemvalue="multidict" />
            <item index="10" class="java.lang.String" itemvalue="yarl" />
            <item index="11" class="java.lang.String" itemvalue="urllib3" />
            <item index="12" class="java.lang.String" itemvalue="idna" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
          <option value="E111" />
          <option value="E125" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>